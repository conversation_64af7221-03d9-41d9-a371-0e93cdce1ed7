<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Report Judging Conflict</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Before reporting a conflict:</strong> Please ensure you have reviewed your scores carefully. 
                        Conflicts should only be reported for legitimate concerns about judging fairness, technical errors, 
                        or procedural issues.
                    </div>

                    <form method="POST" action="<?php echo BASE_URL; ?>/judging_conflict/report">
                        <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="show_id" class="form-label">Show <span class="text-danger">*</span></label>
                                    
                                    <?php if ($user_role === 'admin'): ?>
                                        <!-- Admin gets search interface -->
                                        <div class="position-relative">
                                            <input type="text" id="show_search" class="form-control" 
                                                   placeholder="Search for a show..." autocomplete="off">
                                            <input type="hidden" name="show_id" id="show_id" required>
                                            <div id="show_search_results" class="position-absolute w-100 bg-white border rounded shadow-sm" 
                                                 style="display: none; z-index: 1000; max-height: 200px; overflow-y: auto;"></div>
                                        </div>
                                        <div class="form-text">
                                            Type to search for shows by name, location, or coordinator. 
                                            Showing recent/active shows below:
                                        </div>
                                        
                                        <!-- Fallback dropdown for recent shows -->
                                        <select id="recent_shows" class="form-select mt-2" style="font-size: 0.9em;">
                                            <option value="">Or select from recent/active shows...</option>
                                            <?php foreach ($shows as $show): ?>
                                                <option value="<?php echo $show->id; ?>" 
                                                        <?php echo $selected_show_id == $show->id ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($show->name); ?>
                                                    <?php if ($show->start_date): ?>
                                                        - <?php echo formatDateTimeForUser($show->start_date, $_SESSION['user_id'] ?? null, 'M j, Y'); ?>
                                                    <?php endif; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    <?php else: ?>
                                        <!-- Non-admins get regular dropdown -->
                                        <select name="show_id" id="show_id" class="form-select" required>
                                            <option value="">Select a show...</option>
                                            <?php foreach ($shows as $show): ?>
                                                <option value="<?php echo $show->id; ?>" 
                                                        <?php echo $selected_show_id == $show->id ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($show->name); ?>
                                                    <?php if ($show->start_date): ?>
                                                        - <?php echo formatDateTimeForUser($show->start_date, $_SESSION['user_id'] ?? null, 'M j, Y'); ?>
                                                    <?php endif; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <?php if ($user_role === 'user'): ?>
                                            <div class="form-text">Only shows you're registered for are shown.</div>
                                        <?php elseif ($user_role === 'coordinator'): ?>
                                            <div class="form-text">Only shows you coordinate are shown.</div>
                                        <?php elseif ($user_role === 'judge'): ?>
                                            <div class="form-text">Only shows you're assigned to judge are shown.</div>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <?php if ($user_role === 'user'): ?>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="registration_id" class="form-label">Vehicle Registration</label>
                                        <select name="registration_id" id="registration_id" class="form-select">
                                            <option value="">Select a registration...</option>
                                            <!-- Will be populated via JavaScript -->
                                        </select>
                                        <div class="form-text">Select the vehicle registration this conflict relates to (if applicable).</div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="mb-3">
                            <label for="conflict_type" class="form-label">Conflict Type <span class="text-danger">*</span></label>
                            <select name="conflict_type" id="conflict_type" class="form-select" required>
                                <option value="">Select conflict type...</option>
                                <?php if ($user_role === 'user'): ?>
                                    <option value="owner_complaint">Score Dispute</option>
                                    <option value="technical_error">Technical Error</option>
                                    <option value="scoring_dispute">Judging Process Concern</option>
                                <?php else: ?>
                                    <option value="score_discrepancy">Score Discrepancy</option>
                                    <option value="assignment_conflict">Judge Assignment Conflict</option>
                                    <option value="scoring_dispute">Scoring Dispute</option>
                                    <option value="technical_error">Technical Error</option>
                                    <option value="judge_concern">Judge Concern</option>
                                    <?php if (in_array($user_role, ['admin', 'coordinator'])): ?>
                                        <option value="owner_complaint">Owner Complaint</option>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                        
                        <!-- Conflict Type Descriptions -->
                        <div id="conflict-descriptions" class="mb-3" style="display: none;">
                            <div class="alert alert-light">
                                <div id="desc-owner_complaint" class="conflict-desc" style="display: none;">
                                    <strong>Score Dispute:</strong> Use this when you believe your vehicle's scores are incorrect or unfair compared to similar vehicles.
                                </div>
                                <div id="desc-score_discrepancy" class="conflict-desc" style="display: none;">
                                    <strong>Score Discrepancy:</strong> Significant differences in scores between judges for the same vehicle.
                                </div>
                                <div id="desc-assignment_conflict" class="conflict-desc" style="display: none;">
                                    <strong>Assignment Conflict:</strong> Issues with judge assignments, such as conflicts of interest or double assignments.
                                </div>
                                <div id="desc-scoring_dispute" class="conflict-desc" style="display: none;">
                                    <strong>Scoring Dispute:</strong> Disagreements about scoring criteria or methodology.
                                </div>
                                <div id="desc-technical_error" class="conflict-desc" style="display: none;">
                                    <strong>Technical Error:</strong> System errors, data corruption, or technical issues affecting scores.
                                </div>
                                <div id="desc-judge_concern" class="conflict-desc" style="display: none;">
                                    <strong>Judge Concern:</strong> Concerns raised by judges about the judging process or other judges.
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="title" class="form-label">Conflict Title <span class="text-danger">*</span></label>
                            <input type="text" name="title" id="title" class="form-control" 
                                   placeholder="Brief description of the conflict" required maxlength="255">
                            <div class="form-text">Provide a brief, clear title that summarizes the issue.</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Detailed Description <span class="text-danger">*</span></label>
                            <textarea name="description" id="description" class="form-control" rows="6" 
                                      placeholder="Please provide a detailed description of the conflict..." required></textarea>
                            <div class="form-text">
                                Include specific details such as:
                                <ul class="mb-0 mt-1">
                                    <li>What happened that you believe is incorrect or unfair</li>
                                    <li>Specific scores or judges involved (if known)</li>
                                    <li>Any evidence or observations supporting your concern</li>
                                    <li>What outcome you believe would be fair</li>
                                </ul>
                            </div>
                        </div>
                        
                        <?php if ($user_role === 'user'): ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-clock me-2"></i>
                                <strong>Time Limit:</strong> Conflicts must be reported within 72 hours of results being posted. 
                                After this time, conflicts may not be accepted unless there are exceptional circumstances.
                            </div>
                        <?php endif; ?>
                        
                        <div class="d-flex justify-content-between">
                            <a href="<?php echo BASE_URL; ?>/judging_conflict" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>Submit Conflict Report
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Help Section -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-question-circle me-2"></i>Need Help?</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>What types of conflicts can be reported?</h6>
                            <ul>
                                <li><strong>Score Disputes:</strong> When you believe scores are incorrect or unfair</li>
                                <li><strong>Technical Errors:</strong> System glitches or data problems</li>
                                <li><strong>Process Issues:</strong> Problems with judging procedures</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>What happens after I report?</h6>
                            <ul>
                                <li>Your report is reviewed by show coordinators and admins</li>
                                <li>You'll receive updates on the investigation progress</li>
                                <li>A resolution will be provided within 48-72 hours</li>
                                <li>You can track your report status in your dashboard</li>
                            </ul>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="text-center">
                        <p class="mb-2"><strong>Still have questions?</strong></p>
                        <a href="<?php echo BASE_URL; ?>/contact" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-envelope me-2"></i>Contact Support
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
<?php if ($user_role === 'admin'): ?>
// Admin show search functionality
let searchTimeout;
const showSearchInput = document.getElementById('show_search');
const showIdInput = document.getElementById('show_id');
const searchResults = document.getElementById('show_search_results');
const recentShowsSelect = document.getElementById('recent_shows');

// Search shows as user types
showSearchInput.addEventListener('input', function() {
    const query = this.value.trim();
    
    clearTimeout(searchTimeout);
    
    if (query.length < 2) {
        searchResults.style.display = 'none';
        showIdInput.value = '';
        return;
    }
    
    searchTimeout = setTimeout(() => {
        fetch(`<?php echo BASE_URL; ?>/judging_conflict/searchShows?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                displaySearchResults(data);
            })
            .catch(error => {
                console.error('Search error:', error);
                searchResults.style.display = 'none';
            });
    }, 300);
});

// Display search results
function displaySearchResults(shows) {
    if (!shows || shows.length === 0) {
        searchResults.innerHTML = '<div class="p-2 text-muted">No shows found</div>';
        searchResults.style.display = 'block';
        return;
    }
    
    let html = '';
    shows.forEach(show => {
        html += `
            <div class="search-result-item p-2 border-bottom" style="cursor: pointer;" 
                 onclick="selectShow(${show.id}, '${show.name.replace(/'/g, "\\'")}', '${show.location}', '${show.date}')">
                <div class="fw-bold">${show.name}</div>
                <small class="text-muted">${show.location} • ${show.date} • ${show.coordinator}</small>
            </div>
        `;
    });
    
    searchResults.innerHTML = html;
    searchResults.style.display = 'block';
}

// Select a show from search results
function selectShow(id, name, location, date) {
    showIdInput.value = id;
    showSearchInput.value = `${name} - ${location} (${date})`;
    searchResults.style.display = 'none';
    
    // Clear recent shows selection
    recentShowsSelect.value = '';
}

// Handle recent shows dropdown
recentShowsSelect.addEventListener('change', function() {
    if (this.value) {
        showIdInput.value = this.value;
        const selectedText = this.options[this.selectedIndex].text;
        showSearchInput.value = selectedText;
        searchResults.style.display = 'none';
    }
});

// Hide search results when clicking outside
document.addEventListener('click', function(e) {
    if (!showSearchInput.contains(e.target) && !searchResults.contains(e.target)) {
        searchResults.style.display = 'none';
    }
});

// Add hover effects to search results
document.addEventListener('DOMContentLoaded', function() {
    const style = document.createElement('style');
    style.textContent = `
        .search-result-item:hover {
            background-color: #f8f9fa;
        }
    `;
    document.head.appendChild(style);
});
<?php endif; ?>

// Show conflict type descriptions
document.getElementById('conflict_type').addEventListener('change', function() {
    const selectedType = this.value;
    const descriptionsDiv = document.getElementById('conflict-descriptions');
    const allDescs = document.querySelectorAll('.conflict-desc');
    
    // Hide all descriptions
    allDescs.forEach(desc => desc.style.display = 'none');
    
    if (selectedType) {
        // Show selected description
        const selectedDesc = document.getElementById('desc-' + selectedType);
        if (selectedDesc) {
            selectedDesc.style.display = 'block';
            descriptionsDiv.style.display = 'block';
        }
    } else {
        descriptionsDiv.style.display = 'none';
    }
});

<?php if ($user_role === 'user'): ?>
// Load registrations when show is selected
document.getElementById('show_id').addEventListener('change', function() {
    const showId = this.value;
    const registrationSelect = document.getElementById('registration_id');
    
    // Clear existing options
    registrationSelect.innerHTML = '<option value="">Select a registration...</option>';
    
    if (showId) {
        fetch(`<?php echo BASE_URL; ?>/judging_conflict/getShowRegistrations/${showId}`)
            .then(response => response.json())
            .then(data => {
                if (data.registrations) {
                    data.registrations.forEach(reg => {
                        const option = document.createElement('option');
                        option.value = reg.id;
                        option.textContent = `#${reg.registration_number} - ${reg.year} ${reg.make} ${reg.model}`;
                        if (<?php echo $selected_registration_id ?? 0; ?> == reg.id) {
                            option.selected = true;
                        }
                        registrationSelect.appendChild(option);
                    });
                }
            })
            .catch(error => {
                console.error('Error loading registrations:', error);
            });
    }
});

// Load registrations on page load if show is pre-selected
document.addEventListener('DOMContentLoaded', function() {
    const showSelect = document.getElementById('show_id');
    if (showSelect.value) {
        showSelect.dispatchEvent(new Event('change'));
    }
});
<?php endif; ?>

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const title = document.getElementById('title').value.trim();
    const description = document.getElementById('description').value.trim();
    const conflictType = document.getElementById('conflict_type').value;
    
    if (!title || !description || !conflictType) {
        e.preventDefault();
        alert('Please fill in all required fields.');
        return;
    }
    
    if (description.length < 50) {
        e.preventDefault();
        alert('Please provide a more detailed description (at least 50 characters).');
        return;
    }
    
    // Confirm submission
    if (!confirm('Are you sure you want to submit this conflict report? Please ensure all information is accurate.')) {
        e.preventDefault();
    }
});
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>