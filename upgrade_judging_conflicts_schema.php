<?php
/**
 * Upgrade Judging Conflicts Schema
 * 
 * This script adds the missing columns to get full judging conflicts functionality.
 * It safely adds columns without affecting existing data.
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>🔧 Upgrading Judging Conflicts Schema</h1>";

try {
    $db = new Database();
    
    echo "<h2>📋 Pre-upgrade Check</h2>";
    
    // Check current table structure
    $db->query("DESCRIBE judging_conflicts");
    $columns = $db->resultSet();
    $currentColumns = array_column($columns, 'Field');
    
    echo "<p>Current columns: " . implode(', ', $currentColumns) . "</p>";
    
    // Define columns to add
    $columnsToAdd = [
        'related_score_ids' => "ADD COLUMN `related_score_ids` TEXT DEFAULT NULL COMMENT 'JSON array of score IDs involved' AFTER `description`",
        'related_judge_ids' => "ADD COLUMN `related_judge_ids` TEXT DEFAULT NULL COMMENT 'JSON array of judge IDs involved' AFTER `related_score_ids`",
        'resolved_by_user_id' => "ADD COLUMN `resolved_by_user_id` int(10) unsigned DEFAULT NULL AFTER `resolved_by_admin_id`"
    ];
    
    echo "<h2>🚀 Adding Missing Columns</h2>";
    
    $addedColumns = 0;
    $skippedColumns = 0;
    
    foreach ($columnsToAdd as $columnName => $alterStatement) {
        if (in_array($columnName, $currentColumns)) {
            echo "<p style='color: blue;'>⏭️ Column '{$columnName}' already exists, skipping</p>";
            $skippedColumns++;
            continue;
        }
        
        try {
            $sql = "ALTER TABLE `judging_conflicts` " . $alterStatement;
            echo "<p>Adding column '{$columnName}'...</p>";
            echo "<code style='background: #f5f5f5; padding: 5px; display: block; margin: 5px 0;'>{$sql}</code>";
            
            $db->query($sql);
            $db->execute();
            
            echo "<p style='color: green;'>✅ Successfully added column '{$columnName}'</p>";
            $addedColumns++;
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Failed to add column '{$columnName}': " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>🔗 Adding Foreign Key Constraints</h2>";
    
    // Add foreign key for resolved_by_user_id if it was added
    if (!in_array('resolved_by_user_id', $currentColumns) && $addedColumns > 0) {
        try {
            $constraintSql = "ALTER TABLE `judging_conflicts` 
                             ADD CONSTRAINT `fk_judging_conflicts_resolved_by_user_id` 
                             FOREIGN KEY (`resolved_by_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL";
            
            echo "<p>Adding foreign key constraint for resolved_by_user_id...</p>";
            echo "<code style='background: #f5f5f5; padding: 5px; display: block; margin: 5px 0;'>{$constraintSql}</code>";
            
            $db->query($constraintSql);
            $db->execute();
            
            echo "<p style='color: green;'>✅ Successfully added foreign key constraint</p>";
            
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Foreign key constraint may already exist or failed: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>🔍 Post-upgrade Verification</h2>";
    
    // Check updated table structure
    $db->query("DESCRIBE judging_conflicts");
    $newColumns = $db->resultSet();
    $newColumnNames = array_column($newColumns, 'Field');
    
    echo "<p>Updated columns: " . implode(', ', $newColumnNames) . "</p>";
    
    // Verify the new columns exist
    $targetColumns = ['related_score_ids', 'related_judge_ids', 'resolved_by_user_id'];
    $allPresent = true;
    
    foreach ($targetColumns as $column) {
        if (in_array($column, $newColumnNames)) {
            echo "<p style='color: green;'>✅ {$column} is now present</p>";
        } else {
            echo "<p style='color: red;'>❌ {$column} is still missing</p>";
            $allPresent = false;
        }
    }
    
    echo "<h2>📊 Upgrade Summary</h2>";
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Columns added:</strong> {$addedColumns}</p>";
    echo "<p><strong>Columns skipped:</strong> {$skippedColumns}</p>";
    
    if ($allPresent) {
        echo "<p style='color: green; font-weight: bold;'>🎉 Upgrade completed successfully!</p>";
        echo "<h3>✨ New Features Now Available:</h3>";
        echo "<ul>";
        echo "<li><strong>Direct Score Linking:</strong> Conflicts can now be linked directly to specific scores</li>";
        echo "<li><strong>Judge Involvement Tracking:</strong> Track which judges are involved in each conflict</li>";
        echo "<li><strong>Efficient Queries:</strong> Faster lookups for conflicts involving specific scores/judges</li>";
        echo "<li><strong>Automatic Detection Ready:</strong> System can now automatically detect and link score discrepancies</li>";
        echo "<li><strong>Better Resolution Tracking:</strong> Proper tracking of who resolved conflicts</li>";
        echo "</ul>";
        
        echo "<h3>🔄 Model Update Required:</h3>";
        echo "<p>The JudgingConflictModel needs to be updated to use these new columns for full functionality.</p>";
        
    } else {
        echo "<p style='color: orange; font-weight: bold;'>⚠️ Upgrade partially completed. Some columns may need manual attention.</p>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Critical Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database connection and permissions.</p>";
}

echo "<h2>🧹 Cleanup</h2>";
echo "<p>You can safely delete this file after the upgrade is complete.</p>";
echo "<p><strong>Next step:</strong> Update the JudgingConflictModel to use the new columns for enhanced functionality.</p>";
?>
