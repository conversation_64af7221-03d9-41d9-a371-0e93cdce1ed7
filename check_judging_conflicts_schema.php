<?php
/**
 * Check Judging Conflicts Table Schema
 * 
 * This script checks the current structure of the judging_conflicts table
 * and shows what columns are missing compared to the full schema.
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>🔍 Judging Conflicts Table Schema Check</h1>";

try {
    $db = new Database();
    
    // Check if table exists
    echo "<h2>📋 Table Existence Check</h2>";
    $db->query("SHOW TABLES LIKE 'judging_conflicts'");
    $tableExists = $db->single();
    
    if (!$tableExists) {
        echo "<p style='color: red;'>❌ judging_conflicts table does not exist!</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ judging_conflicts table exists</p>";
    
    // Get current table structure
    echo "<h2>🏗️ Current Table Structure</h2>";
    $db->query("DESCRIBE judging_conflicts");
    $columns = $db->resultSet();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    $currentColumns = [];
    foreach ($columns as $column) {
        $currentColumns[] = $column->Field;
        echo "<tr>";
        echo "<td>{$column->Field}</td>";
        echo "<td>{$column->Type}</td>";
        echo "<td>{$column->Null}</td>";
        echo "<td>{$column->Key}</td>";
        echo "<td>{$column->Default}</td>";
        echo "<td>{$column->Extra}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check for missing columns from full schema
    echo "<h2>🔍 Missing Columns Analysis</h2>";
    
    $expectedColumns = [
        'id', 'show_id', 'registration_id', 'conflict_type', 'reported_by_user_id', 
        'reported_by_role', 'title', 'description', 'related_score_ids', 'related_judge_ids',
        'related_data', 'status', 'priority', 'assigned_to_admin_id', 'resolution_notes',
        'resolution_action', 'resolved_by_user_id', 'resolved_at', 'escalated_at',
        'auto_detected', 'detection_criteria', 'created_at', 'updated_at'
    ];
    
    $missingColumns = array_diff($expectedColumns, $currentColumns);
    $extraColumns = array_diff($currentColumns, $expectedColumns);
    
    if (empty($missingColumns)) {
        echo "<p style='color: green;'>✅ All expected columns are present!</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Missing columns from full schema:</p>";
        echo "<ul>";
        foreach ($missingColumns as $column) {
            echo "<li style='color: red;'><strong>{$column}</strong></li>";
        }
        echo "</ul>";
    }
    
    if (!empty($extraColumns)) {
        echo "<p style='color: blue;'>ℹ️ Extra columns not in expected schema:</p>";
        echo "<ul>";
        foreach ($extraColumns as $column) {
            echo "<li style='color: blue;'>{$column}</li>";
        }
        echo "</ul>";
    }
    
    // Check related tables
    echo "<h2>🔗 Related Tables Check</h2>";
    
    $relatedTables = [
        'judging_conflict_comments',
        'judging_conflict_related_scores', 
        'judging_conflict_related_judges'
    ];
    
    foreach ($relatedTables as $table) {
        $db->query("SHOW TABLES LIKE '{$table}'");
        $exists = $db->single();
        
        if ($exists) {
            echo "<p style='color: green;'>✅ {$table} exists</p>";
            
            // Show structure
            $db->query("DESCRIBE {$table}");
            $tableColumns = $db->resultSet();
            echo "<details><summary>Show {$table} structure</summary>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th></tr>";
            foreach ($tableColumns as $col) {
                echo "<tr><td>{$col->Field}</td><td>{$col->Type}</td><td>{$col->Null}</td><td>{$col->Key}</td></tr>";
            }
            echo "</table></details>";
        } else {
            echo "<p style='color: red;'>❌ {$table} does not exist</p>";
        }
    }
    
    echo "<h2>💡 Recommendations</h2>";
    
    if (in_array('related_score_ids', $missingColumns) || in_array('related_judge_ids', $missingColumns)) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h3>🚨 Critical Missing Features</h3>";
        echo "<p>Your database is missing the <code>related_score_ids</code> and <code>related_judge_ids</code> columns.</p>";
        echo "<p><strong>This means you're missing:</strong></p>";
        echo "<ul>";
        echo "<li>Direct linking of conflicts to specific scores</li>";
        echo "<li>Tracking which judges are involved in conflicts</li>";
        echo "<li>Automatic conflict detection capabilities</li>";
        echo "<li>Efficient conflict resolution workflows</li>";
        echo "</ul>";
        echo "<p><strong>Recommendation:</strong> Add the missing columns to get full functionality.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<h2>🔧 Next Steps</h2>";
echo "<p>If missing columns are found, we can create a migration script to add them safely.</p>";
?>
