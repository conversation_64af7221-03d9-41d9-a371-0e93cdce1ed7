<?php
/**
 * Judging Conflict Controller
 * 
 * This controller handles all judging conflict resolution functionality.
 */
class JudgingConflictController extends Controller {
    private $conflictModel;
    private $showModel;
    private $userModel;
    private $registrationModel;
    private $judgingModel;
    private $customFieldValuesModel;
    private $auth;
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        $this->auth = new Auth();
        if (!$this->auth->isLoggedIn()) {
            $this->redirect('auth/login');
            return;
        }
        
        // Check if user has appropriate role
        if (!$this->auth->hasRole(['admin', 'coordinator', 'judge', 'user'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $this->conflictModel = $this->model('JudgingConflictModel');
        $this->showModel = $this->model('ShowModel');
        $this->userModel = $this->model('UserModel');
        $this->registrationModel = $this->model('RegistrationModel');
        $this->judgingModel = $this->model('JudgingModel');
        $this->customFieldValuesModel = $this->model('CustomFieldValuesModel');
        $this->db = new Database();
    }
    
    /**
     * Default index method - redirects based on user role
     */
    public function index() {
        $userRole = $this->auth->getCurrentUserRole();
        
        if (in_array($userRole, ['admin', 'coordinator'])) {
            $this->redirect('judging_conflict/dashboard');
        } else {
            $this->redirect('judging_conflict/my_reports');
        }
    }
    
    /**
     * Admin/Coordinator dashboard for managing conflicts
     */
    public function dashboard() {
        // Check admin/coordinator access
        if (!$this->auth->hasRole(['admin', 'coordinator'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Get filters from request
        $filters = [
            'status' => $_GET['status'] ?? '',
            'priority' => $_GET['priority'] ?? '',
            'conflict_type' => $_GET['conflict_type'] ?? '',
            'show_id' => $_GET['show_id'] ?? '',
            'search' => $_GET['search'] ?? ''
        ];
        
        // For coordinators, limit to their shows
        if ($userRole === 'coordinator') {
            $coordinatorShows = $this->showModel->getCoordinatorShows($userId);
            $showIds = array_column($coordinatorShows, 'id');
            if (!empty($showIds)) {
                $filters['show_id'] = $showIds;
            } else {
                // Coordinator has no shows, show empty results
                $filters['show_id'] = [0];
            }
        }
        
        $page = (int)($_GET['page'] ?? 1);
        $perPage = 20;
        
        // Get conflicts
        $result = $this->conflictModel->getConflicts($filters, $page, $perPage);
        $conflicts = $result['conflicts'];
        $pagination = $result['pagination'];
        
        // Get statistics
        $stats = $this->conflictModel->getConflictStatistics($filters);
        
        // Get shows for filter dropdown
        if ($userRole === 'admin') {
            // For admins, only get active shows for the filter dropdown
            $shows = $this->showModel->getActiveShows();
            if (empty($shows)) {
                // Fallback to recent shows if no active ones
                $shows = $this->showModel->getRecentShows(100); // Limit to 100 recent shows
            }
        } else {
            $shows = $this->showModel->getShowsByCoordinator($userId);
        }
        
        $data = [
            'title' => 'Judging Conflicts Dashboard',
            'conflicts' => $conflicts,
            'pagination' => $pagination,
            'stats' => $stats,
            'shows' => $shows,
            'filters' => $filters,
            'user_role' => $userRole
        ];
        
        $this->view('judging_conflict/dashboard', $data);
    }
    
    /**
     * View individual conflict details
     */
    public function viewConflict($conflictId) {
        if (!$conflictId) {
            $this->setFlashMessage('error', 'Conflict ID is required.', 'danger');
            $this->redirect('judging_conflict');
            return;
        }
        
        $conflict = $this->conflictModel->getConflictById($conflictId);
        if (!$conflict) {
            $this->setFlashMessage('error', 'Conflict not found.', 'danger');
            $this->redirect('judging_conflict');
            return;
        }
        
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Check access permissions
        $hasAccess = false;
        if (in_array($userRole, ['admin'])) {
            $hasAccess = true;
        } elseif ($userRole === 'coordinator') {
            // Check if coordinator has access to this show
            $hasAccess = $this->auth->hasShowRole($conflict->show_id, 'coordinator');
        } elseif ($userRole === 'judge') {
            // Judge can view if they're involved in the conflict
            $hasAccess = ($conflict->reported_by_user_id == $userId || 
                         in_array($userId, $conflict->related_judge_ids ?? []));
        } else {
            // Regular user can view if they reported it or own the vehicle
            $hasAccess = ($conflict->reported_by_user_id == $userId);
            if (!$hasAccess && $conflict->registration_id) {
                $registration = $this->registrationModel->getRegistrationById($conflict->registration_id);
                $hasAccess = ($registration && $registration->user_id == $userId);
            }
        }
        
        if (!$hasAccess) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get related scores if available
        $relatedScores = [];
        if (!empty($conflict->related_score_ids)) {
            foreach ($conflict->related_score_ids as $scoreId) {
                $score = $this->getScoreById($scoreId);
                if ($score) {
                    $relatedScores[] = $score;
                }
            }
        }
        
        // Get related judges if available
        $relatedJudges = [];
        if (!empty($conflict->related_judge_ids)) {
            foreach ($conflict->related_judge_ids as $judgeId) {
                $judge = $this->userModel->getUserById($judgeId);
                if ($judge) {
                    $relatedJudges[] = $judge;
                }
            }
        }
        
        // Get available admins for assignment (admin only)
        $availableAdmins = [];
        if ($userRole === 'admin') {
            $availableAdmins = $this->userModel->getUsersByRole('admin');
        }
        
        $data = [
            'title' => 'Conflict Details: ' . $conflict->title,
            'conflict' => $conflict,
            'related_scores' => $relatedScores,
            'related_judges' => $relatedJudges,
            'available_admins' => $availableAdmins,
            'user_role' => $userRole,
            'can_edit' => in_array($userRole, ['admin', 'coordinator'])
        ];
        
        $this->view('judging_conflict/view', $data);
    }
    
    /**
     * Report a new conflict (for vehicle owners and judges)
     */
    public function report($showId = null, $registrationId = null) {
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            if (!verifyCsrfToken()) {
                $this->setFlashMessage('error', 'Invalid security token.', 'danger');
                $this->redirect('judging_conflict/report');
                return;
            }
            
            $data = [
                'show_id' => (int)$_POST['show_id'],
                'registration_id' => !empty($_POST['registration_id']) ? (int)$_POST['registration_id'] : null,
                'conflict_type' => $_POST['conflict_type'],
                'title' => trim($_POST['title']),
                'description' => trim($_POST['description']),
                'reported_by_user_id' => $userId,
                'reported_by_role' => $userRole === 'user' ? 'owner' : $userRole
            ];
            
            // Validate required fields
            if (empty($data['show_id']) || empty($data['conflict_type']) || 
                empty($data['title']) || empty($data['description'])) {
                $this->setFlashMessage('error', 'All fields are required.', 'danger');
            } else {
                // Check if user can report for this show/registration
                $canReport = false;
                
                if ($userRole === 'user' && $data['registration_id']) {
                    // Check if user owns this registration
                    $registration = $this->registrationModel->getRegistrationById($data['registration_id']);
                    $canReport = ($registration && $registration->user_id == $userId);
                } elseif (in_array($userRole, ['judge', 'coordinator', 'admin'])) {
                    // Judges, coordinators, and admins can report
                    $canReport = true;
                }
                
                if (!$canReport) {
                    $this->setFlashMessage('error', 'You do not have permission to report conflicts for this registration.', 'danger');
                } else {
                    // Set priority based on conflict type
                    if (in_array($data['conflict_type'], ['technical_error', 'assignment_conflict'])) {
                        $data['priority'] = 'high';
                    } elseif ($data['conflict_type'] === 'owner_complaint') {
                        $data['priority'] = 'normal';
                    } else {
                        $data['priority'] = 'normal';
                    }
                    
                    // Debug: Log the data being sent
                    error_log("JudgingConflict Debug - Creating conflict with data: " . print_r($data, true));

                    $conflictId = $this->conflictModel->createConflict($data);

                    error_log("JudgingConflict Debug - createConflict returned: " . ($conflictId ? $conflictId : 'false'));

                    if ($conflictId) {
                        $this->setFlashMessage('success', 'Your conflict report has been submitted successfully. You will be notified of any updates.');
                        $this->redirect('judging_conflict/viewConflict/' . $conflictId);
                        return;
                    } else {
                        $this->setFlashMessage('error', 'Failed to submit conflict report. Please try again.', 'danger');
                    }
                }
            }
        }
        
        // Get user's shows and registrations
        $shows = [];
        $registrations = [];
        
        if ($userRole === 'user') {
            // Get user's registrations
            $userRegistrations = $this->registrationModel->getUserRegistrations($userId);
            
            // Group by show
            $showIds = array_unique(array_column($userRegistrations, 'show_id'));
            foreach ($showIds as $id) {
                $show = $this->showModel->getShowById($id);
                if ($show) {
                    $shows[] = $show;
                }
            }
            
            $registrations = $userRegistrations;
        } else {
            // Get relevant shows for judges/coordinators/admins
            if ($userRole === 'admin') {
                // For admins, only get recent/active shows to avoid loading thousands
                // Use AJAX search for finding specific shows
                $shows = $this->showModel->getActiveShows(); // Only active shows
                if (empty($shows)) {
                    // Fallback to recent shows if no active ones
                    $shows = $this->showModel->getRecentShows(50); // Limit to 50 recent shows
                }
            } elseif ($userRole === 'coordinator') {
                $shows = $this->showModel->getShowsByCoordinator($userId);
            } elseif ($userRole === 'judge') {
                $shows = $this->showModel->getShowsByJudge($userId);
            }
        }
        
        $data = [
            'title' => 'Report Judging Conflict',
            'shows' => $shows,
            'registrations' => $registrations,
            'selected_show_id' => $showId,
            'selected_registration_id' => $registrationId,
            'user_role' => $userRole,
            // Preserve form data if form was submitted
            'form_data' => $_SERVER['REQUEST_METHOD'] === 'POST' ? $_POST : []
        ];
        
        $this->view('judging_conflict/report', $data);
    }
    
    /**
     * AJAX search for shows (for admins)
     */
    public function searchShows() {
        // Only allow admins to search all shows
        if (!$this->auth->isLoggedIn() || $this->auth->getCurrentUserRole() !== 'admin') {
            http_response_code(403);
            echo json_encode(['error' => 'Access denied']);
            return;
        }
        
        $search = $_GET['q'] ?? '';
        if (strlen($search) < 2) {
            echo json_encode([]);
            return;
        }
        
        try {
            // Search shows using proper SQL joins for custom fields
            // Build query with proper joins and unique parameter names
            $searchTerm = '%' . $search . '%';
            $sql = "SELECT DISTINCT s.*, u.name as coordinator_name
                    FROM shows s
                    LEFT JOIN users u ON s.coordinator_id = u.id
                    LEFT JOIN custom_field_values cfv ON s.id = cfv.show_id
                    WHERE s.status NOT IN ('completed', 'cancelled', 'draft', 'payment_pending')
                    AND (s.name LIKE :search1
                         OR s.description LIKE :search2
                         OR s.location LIKE :search3
                         OR u.name LIKE :search4
                         OR cfv.field_value LIKE :search5)
                    ORDER BY s.start_date DESC
                    LIMIT 20";

            $this->db->query($sql);
            $this->db->bind(':search1', $searchTerm);
            $this->db->bind(':search2', $searchTerm);
            $this->db->bind(':search3', $searchTerm);
            $this->db->bind(':search4', $searchTerm);
            $this->db->bind(':search5', $searchTerm);
            $shows = $this->db->resultSet();



            $results = [];
            foreach ($shows as $show) {
                $results[] = [
                    'id' => $show->id,
                    'name' => $show->name,
                    'location' => $show->location ?? 'Unknown Location',
                    'date' => date('M j, Y', strtotime($show->start_date)),
                    'coordinator' => $show->coordinator_name ?? 'Unknown'
                ];
            }
            
            header('Content-Type: application/json');
            echo json_encode($results);
            
        } catch (Exception $e) {
            http_response_code(500);
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                echo json_encode(['error' => 'Search failed: ' . $e->getMessage()]);
            } else {
                echo json_encode(['error' => 'Search failed']);
            }
        }
    }
    
    /**
     * Update conflict status and resolution
     */
    public function update($conflictId) {
        // Check admin/coordinator access
        if (!$this->auth->hasRole(['admin', 'coordinator'])) {
            $this->jsonResponse(['error' => 'Access denied'], 403);
            return;
        }
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['error' => 'Invalid request method'], 405);
            return;
        }
        
        if (!verifyCsrfToken()) {
            $this->jsonResponse(['error' => 'Invalid security token'], 403);
            return;
        }
        
        $conflict = $this->conflictModel->getConflictById($conflictId);
        if (!$conflict) {
            $this->jsonResponse(['error' => 'Conflict not found'], 404);
            return;
        }
        
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Check access permissions
        if ($userRole === 'coordinator' && !$this->auth->hasShowRole($conflict->show_id, 'coordinator')) {
            $this->jsonResponse(['error' => 'Access denied'], 403);
            return;
        }
        
        $updateData = [];
        
        // Handle status update
        if (isset($_POST['status'])) {
            $allowedStatuses = ['open', 'under_review', 'resolved', 'dismissed', 'escalated'];
            if (in_array($_POST['status'], $allowedStatuses)) {
                $updateData['status'] = $_POST['status'];
                
                // Set resolved_by_user_id if resolving
                if ($_POST['status'] === 'resolved') {
                    $updateData['resolved_by_user_id'] = $userId;
                }
            }
        }
        
        // Handle priority update
        if (isset($_POST['priority'])) {
            $allowedPriorities = ['low', 'normal', 'high', 'urgent'];
            if (in_array($_POST['priority'], $allowedPriorities)) {
                $updateData['priority'] = $_POST['priority'];
            }
        }
        
        // Handle assignment (admin only)
        if (isset($_POST['assigned_to_admin_id']) && $userRole === 'admin') {
            $updateData['assigned_to_admin_id'] = !empty($_POST['assigned_to_admin_id']) ? 
                (int)$_POST['assigned_to_admin_id'] : null;
        }
        
        // Handle resolution notes
        if (isset($_POST['resolution_notes'])) {
            $updateData['resolution_notes'] = trim($_POST['resolution_notes']);
        }
        
        // Handle resolution action
        if (isset($_POST['resolution_action'])) {
            $updateData['resolution_action'] = trim($_POST['resolution_action']);
        }
        
        if (empty($updateData)) {
            $this->jsonResponse(['error' => 'No valid update data provided'], 400);
            return;
        }
        
        $result = $this->conflictModel->updateConflict($conflictId, $updateData);
        
        if ($result) {
            $this->jsonResponse(['success' => true, 'message' => 'Conflict updated successfully']);
        } else {
            $this->jsonResponse(['error' => 'Failed to update conflict'], 500);
        }
    }
    
    /**
     * Add a comment to a conflict
     */
    public function addComment($conflictId) {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['error' => 'Invalid request method'], 405);
            return;
        }
        
        if (!verifyCsrfToken()) {
            $this->jsonResponse(['error' => 'Invalid security token'], 403);
            return;
        }
        
        $conflict = $this->conflictModel->getConflictById($conflictId);
        if (!$conflict) {
            $this->jsonResponse(['error' => 'Conflict not found'], 404);
            return;
        }
        
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Check access permissions (same as view)
        $hasAccess = false;
        if (in_array($userRole, ['admin'])) {
            $hasAccess = true;
        } elseif ($userRole === 'coordinator') {
            $hasAccess = $this->auth->hasShowRole($conflict->show_id, 'coordinator');
        } elseif ($userRole === 'judge') {
            $hasAccess = ($conflict->reported_by_user_id == $userId || 
                         in_array($userId, $conflict->related_judge_ids ?? []));
        } else {
            $hasAccess = ($conflict->reported_by_user_id == $userId);
            if (!$hasAccess && $conflict->registration_id) {
                $registration = $this->registrationModel->getRegistrationById($conflict->registration_id);
                $hasAccess = ($registration && $registration->user_id == $userId);
            }
        }
        
        if (!$hasAccess) {
            $this->jsonResponse(['error' => 'Access denied'], 403);
            return;
        }
        
        $comment = trim($_POST['comment'] ?? '');
        if (empty($comment)) {
            $this->jsonResponse(['error' => 'Comment cannot be empty'], 400);
            return;
        }
        
        $isInternal = (isset($_POST['is_internal']) && $_POST['is_internal'] === '1' && 
                      in_array($userRole, ['admin', 'coordinator']));
        
        $result = $this->conflictModel->addComment($conflictId, $userId, $comment, $isInternal);
        
        if ($result) {
            $this->jsonResponse(['success' => true, 'message' => 'Comment added successfully']);
        } else {
            $this->jsonResponse(['error' => 'Failed to add comment'], 500);
        }
    }
    
    /**
     * Get registrations for a show (AJAX)
     */
    public function getShowRegistrations($showId) {
        if (!$this->isAjax()) {
            $this->jsonResponse(['error' => 'Invalid request'], 400);
            return;
        }
        
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        $registrations = [];
        
        if ($userRole === 'user') {
            // Get only user's registrations for this show
            $userRegistrations = $this->registrationModel->getUserRegistrations($userId);
            foreach ($userRegistrations as $reg) {
                if ($reg->show_id == $showId) {
                    $registrations[] = $reg;
                }
            }
        } else {
            // Get all registrations for this show
            $registrations = $this->registrationModel->getShowRegistrations($showId);
        }
        
        $this->jsonResponse(['registrations' => $registrations]);
    }
    
    /**
     * User's conflict reports
     */
    public function myReports() {
        $userId = $this->auth->getCurrentUserId();
        
        $filters = [
            'reported_by_user_id' => $userId,
            'status' => $_GET['status'] ?? '',
            'search' => $_GET['search'] ?? ''
        ];
        
        $page = (int)($_GET['page'] ?? 1);
        $perPage = 10;
        
        $result = $this->conflictModel->getConflicts($filters, $page, $perPage);
        $conflicts = $result['conflicts'];
        $pagination = $result['pagination'];
        
        $data = [
            'title' => 'My Conflict Reports',
            'conflicts' => $conflicts,
            'pagination' => $pagination,
            'filters' => $filters
        ];
        
        $this->view('judging_conflict/my_reports', $data);
    }
    
    /**
     * Run automatic conflict detection
     */
    public function detectConflicts($showId) {
        // Check admin access
        if (!$this->auth->hasRole(['admin'])) {
            $this->jsonResponse(['error' => 'Access denied'], 403);
            return;
        }
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['error' => 'Invalid request method'], 405);
            return;
        }
        
        if (!verifyCsrfToken()) {
            $this->jsonResponse(['error' => 'Invalid security token'], 403);
            return;
        }
        
        $conflicts = $this->conflictModel->detectScoreDiscrepancies($showId);
        
        $this->jsonResponse([
            'success' => true,
            'message' => count($conflicts) . ' conflicts detected and created',
            'conflicts_created' => count($conflicts)
        ]);
    }
    
    /**
     * Get score by ID (helper method)
     */
    private function getScoreById($scoreId) {
        try {
            $sql = "SELECT s.*, jm.name as metric_name, u.name as judge_name,
                           r.registration_number, v.year, v.make, v.model
                    FROM scores s
                    LEFT JOIN judging_metrics jm ON s.metric_id = jm.id
                    LEFT JOIN users u ON s.judge_id = u.id
                    LEFT JOIN registrations r ON s.registration_id = r.id
                    LEFT JOIN vehicles v ON r.vehicle_id = v.id
                    WHERE s.id = :score_id";
            
            $this->db->query($sql);
            $this->db->bind(':score_id', $scoreId);
            
            return $this->db->single();
            
        } catch (Exception $e) {
            error_log("JudgingConflictController::getScoreById - Error: " . $e->getMessage());
            return false;
        }
    }
}