<?php
/**
 * Judging Conflict Model
 * 
 * This model handles all database operations related to judging conflicts and their resolution.
 */
class JudgingConflictModel {
    private $db;
    private $unifiedMessageModel;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
        
        // Load UnifiedMessageModel for notifications
        require_once APPROOT . '/models/UnifiedMessageModel.php';
        $this->unifiedMessageModel = new UnifiedMessageModel();
    }
    
    /**
     * Create a new judging conflict
     * 
     * @param array $data Conflict data
     * @return int|false Conflict ID on success, false on failure
     */
    public function createConflict($data) {
        try {
            // Ensure required tables exist
            $this->ensureTablesExist();
            
            // Validate required fields
            $required = ['show_id', 'conflict_type', 'reported_by_user_id', 'reported_by_role', 'title', 'description'];
            foreach ($required as $field) {
                if (!isset($data[$field]) || empty($data[$field])) {
                    throw new Exception("Required field missing: $field");
                }
            }
            
            // Set defaults
            $data['status'] = $data['status'] ?? 'open';
            $data['priority'] = $data['priority'] ?? 'normal';
            $data['auto_detected'] = $data['auto_detected'] ?? 0;
            
            // Convert arrays to JSON
            if (isset($data['related_score_ids']) && is_array($data['related_score_ids'])) {
                $data['related_score_ids'] = json_encode($data['related_score_ids']);
            }
            if (isset($data['related_judge_ids']) && is_array($data['related_judge_ids'])) {
                $data['related_judge_ids'] = json_encode($data['related_judge_ids']);
            }
            if (isset($data['related_data']) && is_array($data['related_data'])) {
                $data['related_data'] = json_encode($data['related_data']);
            }
            
            $sql = "INSERT INTO judging_conflicts
                    (show_id, registration_id, conflict_type, reported_by_user_id, reported_by_role,
                     title, description, related_score_ids, related_judge_ids, related_data,
                     status, priority, auto_detected, detection_criteria)
                    VALUES
                    (:show_id, :registration_id, :conflict_type, :reported_by_user_id, :reported_by_role,
                     :title, :description, :related_score_ids, :related_judge_ids, :related_data,
                     :status, :priority, :auto_detected, :detection_criteria)";

            $this->db->query($sql);
            $this->db->bind(':show_id', $data['show_id']);
            $this->db->bind(':registration_id', $data['registration_id'] ?? null);
            $this->db->bind(':conflict_type', $data['conflict_type']);
            $this->db->bind(':reported_by_user_id', $data['reported_by_user_id']);
            $this->db->bind(':reported_by_role', $data['reported_by_role']);
            $this->db->bind(':title', $data['title']);
            $this->db->bind(':description', $data['description']);

            // Handle related scores and judges as JSON arrays
            $relatedScoreIds = isset($data['related_score_ids']) ?
                (is_array($data['related_score_ids']) ? json_encode($data['related_score_ids']) : $data['related_score_ids']) :
                null;
            $relatedJudgeIds = isset($data['related_judge_ids']) ?
                (is_array($data['related_judge_ids']) ? json_encode($data['related_judge_ids']) : $data['related_judge_ids']) :
                null;

            $this->db->bind(':related_score_ids', $relatedScoreIds);
            $this->db->bind(':related_judge_ids', $relatedJudgeIds);
            $this->db->bind(':related_data', $data['related_data'] ?? null);
            $this->db->bind(':status', $data['status']);
            $this->db->bind(':priority', $data['priority']);
            $this->db->bind(':auto_detected', $data['auto_detected']);
            $this->db->bind(':detection_criteria', $data['detection_criteria'] ?? null);
            
            if ($this->db->execute()) {
                $conflictId = $this->db->lastInsertId();
                
                // Send notifications about the new conflict
                $this->sendConflictNotifications($conflictId, 'created');
                
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("JudgingConflictModel: Created conflict ID $conflictId");
                }
                
                return $conflictId;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("JudgingConflictModel::createConflict - Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get conflicts with filtering and pagination
     * 
     * @param array $filters Filter criteria
     * @param int $page Page number
     * @param int $perPage Items per page
     * @return array
     */
    public function getConflicts($filters = [], $page = 1, $perPage = 20) {
        try {
            $where = [];
            $params = [];
            
            // Build WHERE clause based on filters
            if (!empty($filters['show_id'])) {
                $where[] = "jc.show_id = :show_id";
                $params[':show_id'] = $filters['show_id'];
            }
            
            if (!empty($filters['status'])) {
                if (is_array($filters['status'])) {
                    $placeholders = [];
                    foreach ($filters['status'] as $i => $status) {
                        $placeholder = ":status_$i";
                        $placeholders[] = $placeholder;
                        $params[$placeholder] = $status;
                    }
                    $where[] = "jc.status IN (" . implode(',', $placeholders) . ")";
                } else {
                    $where[] = "jc.status = :status";
                    $params[':status'] = $filters['status'];
                }
            }
            
            if (!empty($filters['priority'])) {
                $where[] = "jc.priority = :priority";
                $params[':priority'] = $filters['priority'];
            }
            
            if (!empty($filters['conflict_type'])) {
                $where[] = "jc.conflict_type = :conflict_type";
                $params[':conflict_type'] = $filters['conflict_type'];
            }
            
            if (!empty($filters['reported_by_user_id'])) {
                $where[] = "jc.reported_by_user_id = :reported_by_user_id";
                $params[':reported_by_user_id'] = $filters['reported_by_user_id'];
            }
            
            if (!empty($filters['assigned_to_admin_id'])) {
                $where[] = "jc.assigned_to_admin_id = :assigned_to_admin_id";
                $params[':assigned_to_admin_id'] = $filters['assigned_to_admin_id'];
            }
            
            if (!empty($filters['search'])) {
                $where[] = "(jc.title LIKE :search OR jc.description LIKE :search)";
                $params[':search'] = '%' . $filters['search'] . '%';
            }
            
            $whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';
            
            // Get total count
            $countSql = "SELECT COUNT(*) as total FROM judging_conflicts jc $whereClause";
            $this->db->query($countSql);
            foreach ($params as $key => $value) {
                $this->db->bind($key, $value);
            }
            $totalResult = $this->db->single();
            $total = $totalResult->total;
            
            // Calculate pagination
            $offset = ($page - 1) * $perPage;
            $totalPages = ceil($total / $perPage);
            
            // Get conflicts with related data
            $sql = "SELECT jc.*, 
                           s.name as show_name,
                           s.start_date as show_start_date,
                           reporter.name as reported_by_name,
                           reporter.email as reported_by_email,
                           assignee.name as assigned_to_name,
                           assignee.email as assigned_to_email,
                           resolver.name as resolved_by_name,
                           r.registration_number,
                           r.display_number,
                           v.year, v.make, v.model,
                           owner.name as vehicle_owner_name
                    FROM judging_conflicts jc
                    LEFT JOIN shows s ON jc.show_id = s.id
                    LEFT JOIN users reporter ON jc.reported_by_user_id = reporter.id
                    LEFT JOIN users assignee ON jc.assigned_to_admin_id = assignee.id
                    LEFT JOIN users resolver ON jc.resolved_by_user_id = resolver.id
                    LEFT JOIN registrations r ON jc.registration_id = r.id
                    LEFT JOIN vehicles v ON r.vehicle_id = v.id
                    LEFT JOIN users owner ON r.user_id = owner.id
                    $whereClause
                    ORDER BY 
                        CASE jc.priority 
                            WHEN 'urgent' THEN 1 
                            WHEN 'high' THEN 2 
                            WHEN 'normal' THEN 3 
                            WHEN 'low' THEN 4 
                        END,
                        jc.created_at DESC
                    LIMIT :limit OFFSET :offset";
            
            $this->db->query($sql);
            foreach ($params as $key => $value) {
                $this->db->bind($key, $value);
            }
            $this->db->bind(':limit', $perPage);
            $this->db->bind(':offset', $offset);
            
            $conflicts = $this->db->resultSet();
            
            // Process JSON fields
            foreach ($conflicts as $conflict) {
                $conflict->related_score_ids = $conflict->related_score_ids ? json_decode($conflict->related_score_ids, true) : [];
                $conflict->related_judge_ids = $conflict->related_judge_ids ? json_decode($conflict->related_judge_ids, true) : [];
                $conflict->related_data = $conflict->related_data ? json_decode($conflict->related_data, true) : [];
            }
            
            return [
                'conflicts' => $conflicts,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $total,
                    'total_pages' => $totalPages,
                    'has_next' => $page < $totalPages,
                    'has_prev' => $page > 1
                ]
            ];
            
        } catch (Exception $e) {
            error_log("JudgingConflictModel::getConflicts - Error: " . $e->getMessage());
            return ['conflicts' => [], 'pagination' => []];
        }
    }
    
    /**
     * Get a single conflict by ID
     * 
     * @param int $conflictId Conflict ID
     * @return object|false
     */
    public function getConflictById($conflictId) {
        try {
            $sql = "SELECT jc.*, 
                           s.name as show_name,
                           s.start_date as show_start_date,
                           s.location as show_location,
                           reporter.name as reported_by_name,
                           reporter.email as reported_by_email,
                           assignee.name as assigned_to_name,
                           assignee.email as assigned_to_email,
                           resolver.name as resolved_by_name,
                           r.registration_number,
                           r.display_number,
                           v.year, v.make, v.model,
                           owner.name as vehicle_owner_name,
                           owner.email as vehicle_owner_email
                    FROM judging_conflicts jc
                    LEFT JOIN shows s ON jc.show_id = s.id
                    LEFT JOIN users reporter ON jc.reported_by_user_id = reporter.id
                    LEFT JOIN users assignee ON jc.assigned_to_admin_id = assignee.id
                    LEFT JOIN users resolver ON jc.resolved_by_user_id = resolver.id
                    LEFT JOIN registrations r ON jc.registration_id = r.id
                    LEFT JOIN vehicles v ON r.vehicle_id = v.id
                    LEFT JOIN users owner ON r.user_id = owner.id
                    WHERE jc.id = :conflict_id";
            
            $this->db->query($sql);
            $this->db->bind(':conflict_id', $conflictId);
            $conflict = $this->db->single();
            
            if ($conflict) {
                // Process JSON fields
                $conflict->related_score_ids = $conflict->related_score_ids ? json_decode($conflict->related_score_ids, true) : [];
                $conflict->related_judge_ids = $conflict->related_judge_ids ? json_decode($conflict->related_judge_ids, true) : [];
                $conflict->related_data = $conflict->related_data ? json_decode($conflict->related_data, true) : [];
                
                // Get comments
                $conflict->comments = $this->getConflictComments($conflictId);
                
                // Get attachments
                $conflict->attachments = $this->getConflictAttachments($conflictId);
            }
            
            return $conflict;
            
        } catch (Exception $e) {
            error_log("JudgingConflictModel::getConflictById - Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update conflict status and resolution
     * 
     * @param int $conflictId Conflict ID
     * @param array $data Update data
     * @return bool
     */
    public function updateConflict($conflictId, $data) {
        try {
            $updateFields = [];
            $params = [':conflict_id' => $conflictId];
            
            // Build update fields
            $allowedFields = ['status', 'priority', 'assigned_to_admin_id', 'resolution_notes', 
                             'resolution_action', 'resolved_by_user_id'];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateFields[] = "$field = :$field";
                    $params[":$field"] = $data[$field];
                }
            }
            
            // Handle resolved_at timestamp
            if (isset($data['status']) && $data['status'] === 'resolved') {
                $updateFields[] = "resolved_at = NOW()";
            }
            
            // Handle escalated_at timestamp
            if (isset($data['status']) && $data['status'] === 'escalated') {
                $updateFields[] = "escalated_at = NOW()";
            }
            
            if (empty($updateFields)) {
                return false;
            }
            
            $updateFields[] = "updated_at = NOW()";
            
            $sql = "UPDATE judging_conflicts SET " . implode(', ', $updateFields) . " WHERE id = :conflict_id";
            
            $this->db->query($sql);
            foreach ($params as $key => $value) {
                $this->db->bind($key, $value);
            }
            
            $result = $this->db->execute();
            
            if ($result && isset($data['status'])) {
                // Send notifications about status change
                $this->sendConflictNotifications($conflictId, 'status_changed', $data['status']);
            }
            
            return $result;
            
        } catch (Exception $e) {
            error_log("JudgingConflictModel::updateConflict - Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Add a comment to a conflict
     * 
     * @param int $conflictId Conflict ID
     * @param int $userId User ID
     * @param string $comment Comment text
     * @param bool $isInternal Whether this is an internal comment
     * @return bool
     */
    public function addComment($conflictId, $userId, $comment, $isInternal = false) {
        try {
            $sql = "INSERT INTO judging_conflict_comments 
                    (conflict_id, user_id, comment, is_internal, created_at) 
                    VALUES (:conflict_id, :user_id, :comment, :is_internal, NOW())";
            
            $this->db->query($sql);
            $this->db->bind(':conflict_id', $conflictId);
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':comment', $comment);
            $this->db->bind(':is_internal', $isInternal ? 1 : 0);
            
            $result = $this->db->execute();
            
            if ($result) {
                // Send notifications about new comment
                $this->sendConflictNotifications($conflictId, 'comment_added');
            }
            
            return $result;
            
        } catch (Exception $e) {
            error_log("JudgingConflictModel::addComment - Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get comments for a conflict
     * 
     * @param int $conflictId Conflict ID
     * @param bool $includeInternal Whether to include internal comments
     * @return array
     */
    public function getConflictComments($conflictId, $includeInternal = true) {
        try {
            $whereClause = $includeInternal ? '' : 'AND cc.is_internal = 0';
            
            $sql = "SELECT cc.*, u.name as user_name, u.email as user_email, u.role as user_role
                    FROM judging_conflict_comments cc
                    LEFT JOIN users u ON cc.user_id = u.id
                    WHERE cc.conflict_id = :conflict_id $whereClause
                    ORDER BY cc.created_at ASC";
            
            $this->db->query($sql);
            $this->db->bind(':conflict_id', $conflictId);
            
            return $this->db->resultSet();
            
        } catch (Exception $e) {
            error_log("JudgingConflictModel::getConflictComments - Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get attachments for a conflict
     * 
     * @param int $conflictId Conflict ID
     * @return array
     */
    public function getConflictAttachments($conflictId) {
        try {
            $sql = "SELECT ca.*, u.name as user_name
                    FROM judging_conflict_attachments ca
                    LEFT JOIN users u ON ca.user_id = u.id
                    WHERE ca.conflict_id = :conflict_id
                    ORDER BY ca.created_at ASC";
            
            $this->db->query($sql);
            $this->db->bind(':conflict_id', $conflictId);
            
            return $this->db->resultSet();
            
        } catch (Exception $e) {
            error_log("JudgingConflictModel::getConflictAttachments - Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Detect score discrepancies automatically
     * 
     * @param int $showId Show ID
     * @return array Array of detected conflicts
     */
    public function detectScoreDiscrepancies($showId) {
        try {
            // Get variance threshold from settings
            $threshold = $this->getSettingValue('conflict_score_variance_threshold', 20);
            
            $sql = "SELECT 
                        r.id as registration_id,
                        r.registration_number,
                        r.display_number,
                        v.year, v.make, v.model,
                        AVG(s.score) as avg_score,
                        MIN(s.score) as min_score,
                        MAX(s.score) as max_score,
                        COUNT(DISTINCT s.judge_id) as judge_count,
                        GROUP_CONCAT(DISTINCT s.judge_id) as judge_ids,
                        GROUP_CONCAT(DISTINCT s.id) as score_ids
                    FROM registrations r
                    JOIN vehicles v ON r.vehicle_id = v.id
                    JOIN scores s ON r.id = s.registration_id
                    WHERE r.show_id = :show_id
                    AND s.is_draft = 0
                    GROUP BY r.id
                    HAVING judge_count > 1 
                    AND ((max_score - min_score) / avg_score * 100) > :threshold";
            
            $this->db->query($sql);
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':threshold', $threshold);
            
            $discrepancies = $this->db->resultSet();
            $conflicts = [];
            
            foreach ($discrepancies as $discrepancy) {
                // Check if conflict already exists for this registration
                if (!$this->conflictExists($showId, $discrepancy->registration_id, 'score_discrepancy')) {
                    $variance = (($discrepancy->max_score - $discrepancy->min_score) / $discrepancy->avg_score) * 100;
                    
                    $conflictData = [
                        'show_id' => $showId,
                        'registration_id' => $discrepancy->registration_id,
                        'conflict_type' => 'score_discrepancy',
                        'reported_by_user_id' => 1, // System user
                        'reported_by_role' => 'system',
                        'title' => "Score Discrepancy Detected: {$discrepancy->year} {$discrepancy->make} {$discrepancy->model}",
                        'description' => "Automatic detection found significant score variance ({$variance}%) for vehicle registration #{$discrepancy->registration_number}. Scores range from {$discrepancy->min_score} to {$discrepancy->max_score} with average {$discrepancy->avg_score}.",
                        'related_score_ids' => explode(',', $discrepancy->score_ids),
                        'related_judge_ids' => explode(',', $discrepancy->judge_ids),
                        'related_data' => [
                            'variance_percentage' => $variance,
                            'avg_score' => $discrepancy->avg_score,
                            'min_score' => $discrepancy->min_score,
                            'max_score' => $discrepancy->max_score,
                            'judge_count' => $discrepancy->judge_count
                        ],
                        'priority' => $variance > 50 ? 'high' : 'normal',
                        'auto_detected' => 1,
                        'detection_criteria' => "Score variance threshold: {$threshold}%, Detected variance: {$variance}%"
                    ];
                    
                    $conflictId = $this->createConflict($conflictData);
                    if ($conflictId) {
                        $conflicts[] = $conflictId;
                    }
                }
            }
            
            return $conflicts;
            
        } catch (Exception $e) {
            error_log("JudgingConflictModel::detectScoreDiscrepancies - Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Check if a conflict already exists
     * 
     * @param int $showId Show ID
     * @param int $registrationId Registration ID
     * @param string $conflictType Conflict type
     * @return bool
     */
    private function conflictExists($showId, $registrationId, $conflictType) {
        try {
            $sql = "SELECT COUNT(*) as count FROM judging_conflicts 
                    WHERE show_id = :show_id 
                    AND registration_id = :registration_id 
                    AND conflict_type = :conflict_type 
                    AND status NOT IN ('resolved', 'dismissed')";
            
            $this->db->query($sql);
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':registration_id', $registrationId);
            $this->db->bind(':conflict_type', $conflictType);
            
            $result = $this->db->single();
            return $result->count > 0;
            
        } catch (Exception $e) {
            error_log("JudgingConflictModel::conflictExists - Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send notifications about conflict events
     * 
     * @param int $conflictId Conflict ID
     * @param string $event Event type
     * @param string $additionalData Additional data
     * @return void
     */
    private function sendConflictNotifications($conflictId, $event, $additionalData = null) {
        try {
            $conflict = $this->getConflictById($conflictId);
            if (!$conflict) {
                return;
            }
            
            $recipients = [];
            $subject = '';
            $message = '';
            
            switch ($event) {
                case 'created':
                    $subject = "New Judging Conflict Reported: {$conflict->title}";
                    $message = "A new judging conflict has been reported for {$conflict->show_name}.\n\n";
                    $message .= "Conflict Type: " . ucwords(str_replace('_', ' ', $conflict->conflict_type)) . "\n";
                    $message .= "Priority: " . ucfirst($conflict->priority) . "\n";
                    $message .= "Reported by: {$conflict->reported_by_name} ({$conflict->reported_by_role})\n\n";
                    $message .= "Description:\n{$conflict->description}\n\n";
                    $message .= "View conflict details: " . BASE_URL . "/admin/judging_conflicts/view/{$conflictId}";
                    
                    // Notify admins and coordinators
                    $recipients = $this->getAdminAndCoordinatorUsers($conflict->show_id);
                    break;
                    
                case 'status_changed':
                    $subject = "Conflict Status Updated: {$conflict->title}";
                    $message = "The status of judging conflict #{$conflictId} has been updated to: " . ucfirst($additionalData) . "\n\n";
                    $message .= "Show: {$conflict->show_name}\n";
                    $message .= "Conflict: {$conflict->title}\n\n";
                    $message .= "View conflict details: " . BASE_URL . "/admin/judging_conflicts/view/{$conflictId}";
                    
                    // Notify reporter and assigned admin
                    $recipients[] = $conflict->reported_by_user_id;
                    if ($conflict->assigned_to_admin_id) {
                        $recipients[] = $conflict->assigned_to_admin_id;
                    }
                    break;
                    
                case 'comment_added':
                    $subject = "New Comment on Conflict: {$conflict->title}";
                    $message = "A new comment has been added to judging conflict #{$conflictId}.\n\n";
                    $message .= "Show: {$conflict->show_name}\n";
                    $message .= "Conflict: {$conflict->title}\n\n";
                    $message .= "View conflict details: " . BASE_URL . "/admin/judging_conflicts/view/{$conflictId}";
                    
                    // Notify all involved parties
                    $recipients[] = $conflict->reported_by_user_id;
                    if ($conflict->assigned_to_admin_id) {
                        $recipients[] = $conflict->assigned_to_admin_id;
                    }
                    break;
            }
            
            // Remove duplicates and send notifications
            $recipients = array_unique($recipients);
            foreach ($recipients as $userId) {
                if ($userId) {
                    $this->unifiedMessageModel->sendMessage(
                        1, // System user
                        $userId,
                        $subject,
                        $message,
                        $conflict->show_id,
                        'judging'
                    );
                }
            }
            
        } catch (Exception $e) {
            error_log("JudgingConflictModel::sendConflictNotifications - Error: " . $e->getMessage());
        }
    }
    
    /**
     * Get admin and coordinator users for a show
     * 
     * @param int $showId Show ID
     * @return array
     */
    private function getAdminAndCoordinatorUsers($showId) {
        try {
            $sql = "SELECT DISTINCT u.id
                    FROM users u
                    WHERE u.role IN ('admin', 'coordinator')
                    AND u.id IN (
                        SELECT DISTINCT user_id FROM show_role_assignments 
                        WHERE show_id = :show_id AND role IN ('admin', 'coordinator')
                        UNION
                        SELECT DISTINCT id FROM users WHERE role = 'admin'
                    )";
            
            $this->db->query($sql);
            $this->db->bind(':show_id', $showId);
            $users = $this->db->resultSet();
            
            return array_column($users, 'id');
            
        } catch (Exception $e) {
            error_log("JudgingConflictModel::getAdminAndCoordinatorUsers - Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get setting value
     * 
     * @param string $name Setting name
     * @param mixed $default Default value
     * @return mixed
     */
    private function getSettingValue($name, $default = null) {
        try {
            $sql = "SELECT value FROM settings WHERE name = :name";
            $this->db->query($sql);
            $this->db->bind(':name', $name);
            $result = $this->db->single();
            
            return $result ? $result->value : $default;
            
        } catch (Exception $e) {
            error_log("JudgingConflictModel::getSettingValue - Error: " . $e->getMessage());
            return $default;
        }
    }
    
    /**
     * Ensure required tables exist
     * 
     * @return void
     */
    private function ensureTablesExist() {
        try {
            // Check if judging_conflicts table exists
            $sql = "SHOW TABLES LIKE 'judging_conflicts'";
            $this->db->query($sql);
            $result = $this->db->single();
            
            if (!$result) {
                // Run the migration
                $migrationFile = APPROOT . '/database/migrations/create_judging_conflicts_table.sql';
                if (file_exists($migrationFile)) {
                    $sql = file_get_contents($migrationFile);
                    $this->db->query($sql);
                    $this->db->execute();
                }
            }
            
        } catch (Exception $e) {
            error_log("JudgingConflictModel::ensureTablesExist - Error: " . $e->getMessage());
        }
    }
    
    /**
     * Add score IDs to a conflict
     *
     * @param int $conflictId Conflict ID
     * @param array $scoreIds Array of score IDs to add
     * @return bool Success status
     */
    public function addRelatedScores($conflictId, $scoreIds) {
        try {
            // Get current related scores
            $conflict = $this->getConflictById($conflictId);
            if (!$conflict) return false;

            $currentScores = $conflict->related_score_ids ? json_decode($conflict->related_score_ids, true) : [];
            $newScores = array_unique(array_merge($currentScores, $scoreIds));

            $this->db->query("UPDATE judging_conflicts SET related_score_ids = :score_ids WHERE id = :id");
            $this->db->bind(':score_ids', json_encode($newScores));
            $this->db->bind(':id', $conflictId);

            return $this->db->execute();
        } catch (Exception $e) {
            error_log("Error in JudgingConflictModel::addRelatedScores: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Add judge IDs to a conflict
     *
     * @param int $conflictId Conflict ID
     * @param array $judgeIds Array of judge IDs to add
     * @return bool Success status
     */
    public function addRelatedJudges($conflictId, $judgeIds) {
        try {
            // Get current related judges
            $conflict = $this->getConflictById($conflictId);
            if (!$conflict) return false;

            $currentJudges = $conflict->related_judge_ids ? json_decode($conflict->related_judge_ids, true) : [];
            $newJudges = array_unique(array_merge($currentJudges, $judgeIds));

            $this->db->query("UPDATE judging_conflicts SET related_judge_ids = :judge_ids WHERE id = :id");
            $this->db->bind(':judge_ids', json_encode($newJudges));
            $this->db->bind(':id', $conflictId);

            return $this->db->execute();
        } catch (Exception $e) {
            error_log("Error in JudgingConflictModel::addRelatedJudges: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Find conflicts involving specific scores
     *
     * @param array $scoreIds Array of score IDs to search for
     * @return array Array of conflicts
     */
    public function getConflictsByScoreIds($scoreIds) {
        try {
            $sql = "SELECT c.*, s.name as show_name, u.name as reported_by_name
                    FROM judging_conflicts c
                    LEFT JOIN shows s ON c.show_id = s.id
                    LEFT JOIN users u ON c.reported_by_user_id = u.id
                    WHERE ";

            $conditions = [];
            foreach ($scoreIds as $index => $scoreId) {
                $conditions[] = "JSON_CONTAINS(c.related_score_ids, :score_id_{$index})";
            }

            $sql .= implode(' OR ', $conditions);
            $sql .= " ORDER BY c.created_at DESC";

            $this->db->query($sql);
            foreach ($scoreIds as $index => $scoreId) {
                $this->db->bind(":score_id_{$index}", '"' . $scoreId . '"');
            }

            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log("Error in JudgingConflictModel::getConflictsByScoreIds: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Find conflicts involving specific judges
     *
     * @param array $judgeIds Array of judge IDs to search for
     * @return array Array of conflicts
     */
    public function getConflictsByJudgeIds($judgeIds) {
        try {
            $sql = "SELECT c.*, s.name as show_name, u.name as reported_by_name
                    FROM judging_conflicts c
                    LEFT JOIN shows s ON c.show_id = s.id
                    LEFT JOIN users u ON c.reported_by_user_id = u.id
                    WHERE ";

            $conditions = [];
            foreach ($judgeIds as $index => $judgeId) {
                $conditions[] = "JSON_CONTAINS(c.related_judge_ids, :judge_id_{$index})";
            }

            $sql .= implode(' OR ', $conditions);
            $sql .= " ORDER BY c.created_at DESC";

            $this->db->query($sql);
            foreach ($judgeIds as $index => $judgeId) {
                $this->db->bind(":judge_id_{$index}", '"' . $judgeId . '"');
            }

            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log("Error in JudgingConflictModel::getConflictsByJudgeIds: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get conflict statistics
     *
     * @param array $filters Filter criteria
     * @return array
     */
    public function getConflictStatistics($filters = []) {
        try {
            $where = [];
            $params = [];
            
            if (!empty($filters['show_id'])) {
                $where[] = "show_id = :show_id";
                $params[':show_id'] = $filters['show_id'];
            }
            
            if (!empty($filters['date_from'])) {
                $where[] = "created_at >= :date_from";
                $params[':date_from'] = $filters['date_from'];
            }
            
            if (!empty($filters['date_to'])) {
                $where[] = "created_at <= :date_to";
                $params[':date_to'] = $filters['date_to'];
            }
            
            $whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';
            
            $sql = "SELECT 
                        COUNT(*) as total_conflicts,
                        SUM(CASE WHEN status = 'open' THEN 1 ELSE 0 END) as open_conflicts,
                        SUM(CASE WHEN status = 'under_review' THEN 1 ELSE 0 END) as under_review_conflicts,
                        SUM(CASE WHEN status = 'resolved' THEN 1 ELSE 0 END) as resolved_conflicts,
                        SUM(CASE WHEN status = 'dismissed' THEN 1 ELSE 0 END) as dismissed_conflicts,
                        SUM(CASE WHEN status = 'escalated' THEN 1 ELSE 0 END) as escalated_conflicts,
                        SUM(CASE WHEN priority = 'urgent' THEN 1 ELSE 0 END) as urgent_conflicts,
                        SUM(CASE WHEN priority = 'high' THEN 1 ELSE 0 END) as high_priority_conflicts,
                        SUM(CASE WHEN auto_detected = 1 THEN 1 ELSE 0 END) as auto_detected_conflicts,
                        AVG(CASE WHEN resolved_at IS NOT NULL THEN 
                            TIMESTAMPDIFF(HOUR, created_at, resolved_at) 
                        END) as avg_resolution_hours
                    FROM judging_conflicts 
                    $whereClause";
            
            $this->db->query($sql);
            foreach ($params as $key => $value) {
                $this->db->bind($key, $value);
            }
            
            $stats = $this->db->single();
            
            // Get conflicts by type
            $typeSql = "SELECT conflict_type, COUNT(*) as count 
                       FROM judging_conflicts 
                       $whereClause 
                       GROUP BY conflict_type";
            
            $this->db->query($typeSql);
            foreach ($params as $key => $value) {
                $this->db->bind($key, $value);
            }
            
            $typeStats = $this->db->resultSet();
            $stats->by_type = $typeStats;
            
            return $stats;
            
        } catch (Exception $e) {
            error_log("JudgingConflictModel::getConflictStatistics - Error: " . $e->getMessage());
            return (object)[];
        }
    }
}